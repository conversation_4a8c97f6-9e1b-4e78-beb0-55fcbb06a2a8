'use client';
import PropagaButton from '@/components/atoms/Button';
import PropagaInput from '@/components/atoms/Input';
import ContactAlert from '@/components/molecules/ContactAlert';
import Loading from '@/components/molecules/Loading';
import UserSettingsMenu from '@/components/molecules/UserSettingsMenu';
import ErrorDisplay from '@/components/organisms/ErrorDisplay';
import TransactionCardList from '@/components/organisms/TransactionCardList';
import { DEFAULT_ERROR_MESSAGE, EMPTY_SPACE, ENTER, ErrorCodes, TAB } from '@/constants';
import { useWholesalerContext } from '@/context/wholesaler-context';
import { ErrorResponse } from '@/interfaces/error-response';
import { useServices } from '@/services';
import { generateIdentifier } from '@/utils/identifier-operations';
import { numberToCurrencyString } from '@/utils/string-operations';
import useUserPropaga from '@/utils/use-user-propaga';
import {
  Flex,
  FormControl,
  FormErrorMessage,
  FormHelperText,
  FormLabel,
  Heading,
  Image,
  Stack,
  Text,
} from '@chakra-ui/react';
import { useClerk, useUser } from '@clerk/nextjs';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useRouter } from 'next/navigation';
import { useEffect, useRef, useState } from 'react';

export default function Page() {
  const router = useRouter();
  const [customerId, setCustomerId] = useState(EMPTY_SPACE);
  const [amount, setAmount] = useState(0);
  const [cornerStoreId, setCornerStoreId] = useState(EMPTY_SPACE);
  const [creditLimitAvailable, setCreditLimitAvailable] = useState(0);
  const [isInputLoading, setIsInputLoading] = useState(false);
  const [isExternalIdInvalid, setIsExternalIdInvalid] = useState(false);
  const [errorMessage, setErrorMessage] = useState(EMPTY_SPACE);
  const [inputErrorMessage, setInputErrorMessage] = useState(EMPTY_SPACE);
  const customerIdRef = useRef(null);
  const amountRef = useRef(null);
  const { signOut } = useClerk();
  const { userbranchStores, username } = useUserPropaga();

  const { user } = useUser();
  const { transactionService, cornerStoreService } = useServices();
  const { wholesalerName, wholesalerLogo, removeWholesaler } = useWholesalerContext();

  const { data, isLoading } = useQuery({
    queryKey: ['transactions', user?.id],
    queryFn: () =>
      transactionService.getTransactionsByWholesalerUserId(user?.username || EMPTY_SPACE),
    enabled: !!wholesalerName && !!user?.username,
  });

  useEffect(() => {
    if (errorMessage) {
      return;
    }

    if (customerIdRef.current) {
      (customerIdRef.current as HTMLInputElement).focus();
    }
  }, [data, errorMessage]);

  const getCornerStore = useMutation({
    mutationFn: cornerStoreService.getCornerStoreByExternalId,
    onSuccess: (data) => {
      if (data.status === 'in-waiting-list') {
        setIsExternalIdInvalid(true);
        setIsInputLoading(false);
        setCornerStoreId(EMPTY_SPACE);
        setCreditLimitAvailable(0);
        setAmount(0);

        setInputErrorMessage('Este cliente está en la lista de espera. No puede realizar compras.');
        return;
      }

      setIsExternalIdInvalid(false);
      setIsInputLoading(false);
      setCornerStoreId(data.cornerStoreId);
      setCreditLimitAvailable(data.creditLimitAvailable);
      setAmount(0);
    },
    onError: () => {
      setIsExternalIdInvalid(true);
      setIsInputLoading(false);
      setCornerStoreId(EMPTY_SPACE);
      setCreditLimitAvailable(0);
      setAmount(0);
      setInputErrorMessage('Este cliente no está registrado en Propaga.');
    },
  });

  const createTransaction = useMutation({
    mutationFn: transactionService.createTransaction,
    onSuccess: (data) => {
      return router.push(
        `/transaction/${data.transactionId}/verification/${data.verificationId}?totalAmount=${amount}`,
      );
    },
    onError: ({ response }: ErrorResponse) => {
      const errorMessageMap: { [key: string]: string } = {
        [ErrorCodes.CREDIT_LIMIT_EXCEEDED.code]: ErrorCodes.CREDIT_LIMIT_EXCEEDED.message,
        [ErrorCodes.USER_IS_IN_DEFAULT.code]: ErrorCodes.USER_IS_IN_DEFAULT.message,
        [ErrorCodes.MINIMAL_TRANSACTION_REQUIRED.code]:
          ErrorCodes.MINIMAL_TRANSACTION_REQUIRED.message,
      };

      const message = errorMessageMap[response.data.errorCode] || DEFAULT_ERROR_MESSAGE;
      setErrorMessage(message);
    },
  });

  if (isLoading || !user || !data) {
    return <Loading />;
  }

  if (errorMessage) {
    return (
      <ErrorDisplay
        message={errorMessage}
        referenceNumber={EMPTY_SPACE}
        onClick={() => {
          setErrorMessage(EMPTY_SPACE);
          setCornerStoreId(EMPTY_SPACE);
          setCustomerId(EMPTY_SPACE);
        }}
      />
    );
  }

  const handleOnSubmit = async () => {
    if (!cornerStoreId || !amount) {
      return;
    }

    await createTransaction.mutateAsync({
      cornerStoreId,
      totalAmount: amount,
      wholesalerUserId: username,
      products: [],
      wholesalerTransactionId: generateIdentifier(),
      branchStore: userbranchStores ? userbranchStores[0] : EMPTY_SPACE,
    });
  };

  const handleKeyDownCustomerId = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key !== ENTER && event.key !== TAB) {
      return;
    }
    setIsInputLoading(true);
    event.preventDefault();

    await getCornerStore.mutateAsync(customerId);

    if (amountRef && amountRef.current) {
      (amountRef.current as HTMLInputElement).focus();
    }
  };

  const handleKeyDownAmount = async (event: React.KeyboardEvent<HTMLInputElement>) => {
    if (event.key !== ENTER && event.key !== TAB) {
      return;
    }

    event.preventDefault();
    await handleOnSubmit();
  };

  const handleSingOut = () => {
    removeWholesaler();
    signOut();
  };

  return (
    <Flex flexDirection={'column'} h={'100vh'} overflow="hidden">
      <Flex flex={1} overflow="hidden">
        <Flex w={'50%'} py={12} px={20} overflow="auto">
          <Stack spacing={12} w="100%">
            <Image w={'12vh'} src={wholesalerLogo} />
            <Stack spacing={2}>
              <Heading size={'lg'}>Generar orden</Heading>
              <Text>El cliente pagará después con la app de Propaga</Text>
            </Stack>
            <FormControl autoFocus isInvalid={isExternalIdInvalid}>
              <FormLabel fontSize={'lg'}>Número de cliente</FormLabel>
              <PropagaInput
                ref={customerIdRef}
                onChange={(event) => setCustomerId(event.target.value)}
                onKeyDown={handleKeyDownCustomerId}
                isLoading={isInputLoading}
                isSuccess={cornerStoreId !== EMPTY_SPACE}
              />
              {creditLimitAvailable > 0 && (
                <FormHelperText fontSize={'md'}>
                  Saldo disponible: {numberToCurrencyString(creditLimitAvailable)}
                </FormHelperText>
              )}
              <FormErrorMessage fontSize={'md'}>{inputErrorMessage}</FormErrorMessage>
            </FormControl>
            <FormControl isInvalid={amount > 0 && amount > creditLimitAvailable}>
              <FormLabel fontSize={'lg'}>Monto</FormLabel>
              <PropagaInput
                ref={amountRef}
                isCurrencyInput
                onChange={(event) =>
                  setAmount(parseFloat(event.target.value.replace(',', EMPTY_SPACE)))
                }
                onKeyDown={handleKeyDownAmount}
              />
              <FormErrorMessage fontSize={'md'}>
                Monto superior al saldo disponible
              </FormErrorMessage>
            </FormControl>
            <PropagaButton onClick={handleOnSubmit} isDisabled={!cornerStoreId || !amount}>
              Generar
            </PropagaButton>
          </Stack>
        </Flex>
        <Flex w={'50%'} py={12} px={20} overflow={'auto'}>
          <Stack spacing={4} w="100%">
            <Heading size={'lg'}>Historial de órdenes</Heading>
            <ContactAlert />
            <TransactionCardList transactions={data} createdBy={user?.fullName || EMPTY_SPACE} />
          </Stack>
        </Flex>
      </Flex>
      <Flex justifyContent={'flex-end'} p={4}>
        <UserSettingsMenu onSignOut={handleSingOut} />
      </Flex>
    </Flex>
  );
}
