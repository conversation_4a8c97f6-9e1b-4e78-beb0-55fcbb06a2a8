'use client';
import {
  Box,
  Container,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  Text,
  IconButton,
  Stack,
  Heading,
  Accordion,
  AccordionItem,
  AccordionButton,
  AccordionIcon,
  AccordionPanel,
} from '@chakra-ui/react';

import 'react-datepicker/dist/react-datepicker.css';
import { ChevronDownIcon, ChevronRightIcon } from '@chakra-ui/icons';
import { useState } from 'react';
import { InstantSearch, Configure, InfiniteHits } from 'react-instantsearch';
import { searchClient, transformElementsAndGroup } from '@/utils/algolia-client';
import SearchFilters from '@/components/organisms/SearchFilters';
import { formatCurrency, stringToShortDateFormatted } from '@/utils/date-operations';
import { useWholesalerContext } from '@/context/wholesaler-context';

export default function Dashboard() {
  const [expandedRows, setExpandedRows] = useState<{ [key: string]: boolean }>({});
  const { wholesalerName } = useWholesalerContext();

  const toggleRow = (date: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [date]: !prev[date],
    }));
  };

  return (
    <InstantSearch
      searchClient={searchClient}
      indexName={`transaction_${wholesalerName.toLowerCase()}_${process.env.NEXT_PUBLIC_APP_ENV}`}
    >
      <Configure hitsPerPage={50} />
      <Container maxW={'7xl'} py={8}>
        <Box overflowX={'auto'}>
          <Stack spacing={4}>
            <Box bg="white" p={6} borderRadius="lg" shadow="sm" mb={6}>
              <Heading size="lg" mb={6}>
                Conciliación
              </Heading>
              <SearchFilters />
            </Box>

            <InfiniteHits
              showPrevious={false}
              translations={{
                showMoreButtonText: 'Mostrar más resultados',
              }}
              transformItems={transformElementsAndGroup}
              hitComponent={({ hit }) => (
                <Table variant={'ghost'} size="sm" mt={8}>
                  <Thead>
                    <Tr>
                      <Th></Th>
                      <Th colSpan={2} fontSize="sm">
                        Sucursal
                      </Th>
                      <Th colSpan={2} fontSize="sm">
                        Fecha
                      </Th>
                      <Th colSpan={1} isNumeric fontSize="sm">
                        Total de pedidos
                      </Th>
                      <Th isNumeric fontSize="sm">
                        Monto total
                      </Th>
                    </Tr>
                  </Thead>
                  <Tbody m={8}>
                    <Tr
                      key={hit.date}
                      bg="gray.50"
                      style={{ borderRadius: '8px', overflow: 'hidden' }}
                      px={4}
                    >
                      <Td width={'40px'} p={2} borderLeftRadius={'8px'}>
                        <IconButton
                          aria-label="Expand row"
                          icon={expandedRows[hit.date] ? <ChevronDownIcon /> : <ChevronRightIcon />}
                          variant={'ghost'}
                          size={'sm'}
                          onClick={() => toggleRow(hit.date)}
                        />
                      </Td>
                      <Td colSpan={2}>
                        <Text fontSize="sm">{hit.branchStore}</Text>
                      </Td>
                      <Td colSpan={2}>
                        <Text fontWeight={'medium'} fontSize="sm">
                          {stringToShortDateFormatted(hit.date)}
                        </Text>
                      </Td>
                      <Td isNumeric colSpan={1} fontSize="sm">
                        {hit.quantity}
                      </Td>
                      <Td isNumeric borderRightRadius={'8px'} colSpan={2} fontSize="sm">
                        {formatCurrency(hit?.totalAmount)}
                      </Td>
                    </Tr>
                    {expandedRows[hit.date] && (
                      <>
                        <Tr bg="gray.100">
                          <Td></Td>
                          <Td colSpan={6} fontWeight="medium" fontSize="sm">
                            <Accordion key={hit.branchStore} allowMultiple>
                              <AccordionItem border="none">
                                <AccordionButton px={0} py={2}>
                                  <Box flex="1" textAlign="left">
                                    {hit.wholesalerUserId} ({hit.transactions.length} transacciones)
                                  </Box>
                                  <AccordionIcon />
                                </AccordionButton>
                                <AccordionPanel p={0}>
                                  <Table variant="unstyled" size="sm">
                                    <Thead>
                                      <Tr>
                                        <Th fontSize="sm">Folio</Th>
                                        <Th fontSize="sm">Fecha</Th>
                                        <Th fontSize="sm">Sucursal</Th>
                                        <Th fontSize="sm">ID</Th>
                                        <Th fontSize="sm">ID Cajero</Th>
                                        <Th isNumeric fontSize="sm">
                                          Total
                                        </Th>
                                      </Tr>
                                    </Thead>
                                    <Tbody>
                                      {hit.transactions.map((transaction: any) => (
                                        <Tr key={transaction.transactionId}>
                                          <Td fontSize="sm">
                                            {transaction.wholesalerTransactionId}
                                          </Td>
                                          <Td fontSize="sm">
                                            {stringToShortDateFormatted(transaction.movementDate)}
                                          </Td>
                                          <Td fontSize="sm">{hit.branchStore}</Td>
                                          <Td fontSize="sm">{transaction.externalId}</Td>
                                          <Td fontSize="sm">{transaction.wholesalerUserId}</Td>
                                          <Td isNumeric fontSize="sm">
                                            {formatCurrency(transaction.totalAmount)}
                                          </Td>
                                        </Tr>
                                      ))}
                                    </Tbody>
                                  </Table>
                                </AccordionPanel>
                              </AccordionItem>
                            </Accordion>
                          </Td>
                        </Tr>
                      </>
                    )}
                  </Tbody>
                </Table>
              )}
            />
          </Stack>
        </Box>
      </Container>
    </InstantSearch>
  );
}
