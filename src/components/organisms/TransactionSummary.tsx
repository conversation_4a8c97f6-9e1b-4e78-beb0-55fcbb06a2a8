import { Transaction } from '@/interfaces/transaction';
import { stringToLongDateFormatted, stringToMediumDateFormatted } from '@/utils/date-operations';
import { numberToCurrencyString } from '@/utils/string-operations';
import { InfoOutlineIcon } from '@chakra-ui/icons';
import { Alert, Card, CardBody, Divider, Heading, SimpleGrid, Stack, Text } from '@chakra-ui/react';
import ContactAlert from '../molecules/ContactAlert';

const TransactionSummary = ({ transaction }: { transaction: Transaction }) => {
  return (
    <Card variant={'outline'} borderRadius={12} w={'100%'}>
      <CardBody px={12} py={8}>
        <Stack spacing={2} w={'100%'}>
          <ContactAlert />
          <SimpleGrid columns={2} spacing={4}>
            <Text>Código de referencia</Text>
            <Text textAlign={'right'}>{transaction.wholesalerTransactionId}</Text>
          </SimpleGrid>
          <Divider my={4} w={'100%'} />
          <SimpleGrid columns={2} spacing={4}>
            <Text>Plazo</Text>
            <Text textAlign={'right'}>15 días</Text>

            <Text>Fecha límite de pago</Text>
            <Text textAlign={'right'}>{stringToMediumDateFormatted(transaction.paymentDate)}</Text>
          </SimpleGrid>
          <Divider my={4} w={'100%'} />
          <Heading size={'md'} pb={4}>
            Detalle de la transacción
          </Heading>
          <SimpleGrid columns={2} spacing={4}>
            <Text>Monto de compra</Text>
            <Text textAlign={'right'} fontWeight={'bold'}>
              {numberToCurrencyString(transaction.totalAmount)}
            </Text>
          </SimpleGrid>
          <Alert mt={2} backgroundColor={'#F7F9FC'} borderRadius={8} p={4}>
            <InfoOutlineIcon boxSize={5} />
            <Text fontSize={'sm'} pl={4}>
              El cliente pagará después con la app de Propaga
            </Text>
          </Alert>
          <Divider my={4} w={'100%'} />
          <SimpleGrid columns={2} spacing={4}>
            <Text>Fecha y hora de compra</Text>
            <Text textAlign={'right'}>{stringToLongDateFormatted(transaction.movementDate)}</Text>
          </SimpleGrid>
        </Stack>
      </CardBody>
    </Card>
  );
};

export default TransactionSummary;
