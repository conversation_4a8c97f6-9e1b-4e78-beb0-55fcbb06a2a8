import { Box, Stack, FormControl, FormLabel } from '@chakra-ui/react';
import DownloadTransactionsCSV from '../molecules/DownloadTransactionsCSV';
import SelectFilterCustom from '../atoms/SelectFilterCustom';
import SearchBoxCustom from '../atoms/SearchBoxCustom';
import DateRangeFilterCustom from '../atoms/DateRangeFilterCustom';
import { useHits } from 'react-instantsearch';

const SearchFilters = () => {
  const { hits } = useHits();
  return (
    <Stack spacing={4} direction={'column'} display={'flex'} align={'stretch'}>
      <Stack direction={{ base: 'column', md: 'row' }} spacing={4} flex={1}>
        <Box flex={1}>
          <FormLabel fontSize="sm" fontWeight="medium" mb={2}>
            Sucursal
          </FormLabel>
          <SelectFilterCustom attribute="branchStore" />
        </Box>
        <Box flex={1}>
          <FormLabel fontSize="sm" fontWeight="medium" mb={2}>
            ID de Cajero
          </FormLabel>
          <SelectFilterCustom attribute="wholesalerUserId" />
        </Box>
        <Box flex={1}>
          <FormLabel fontSize="sm" fontWeight="medium" mb={2}>
            Fecha
          </FormLabel>
          <DateRangeFilterCustom attribute={'movementDate'} />
        </Box>
      </Stack>

      <Stack direction={{ base: 'column', md: 'row' }} spacing={4} flex={1}>
        <FormControl flex={1}>
          <FormLabel fontSize="sm" fontWeight="medium">
            Buscar
          </FormLabel>
          <Box position="relative">
            <SearchBoxCustom />
          </Box>
        </FormControl>

        <Box alignSelf={{ base: 'stretch', md: 'flex-end' }}>
          <DownloadTransactionsCSV data={hits} />
        </Box>
      </Stack>
    </Stack>
  );
};

export default SearchFilters;
