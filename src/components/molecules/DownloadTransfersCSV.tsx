import { DownloadIcon } from '@chakra-ui/icons';
import { Button } from '@chakra-ui/react';
import { stringToLongDateCSVFormatted } from '@/utils/date-operations';

const DownloadTransfersCSV = ({ data }: { data: any }) => {
  const handleDownloadCSV = (allTransactions: any[]) => {
    const headers = [
      'ID de Transacción',
      'ID externo',
      'Monto',
      'ID de Usuario',
      'ID de Cajero',
      'Sucursal',
      'Fecha',
    ];
    const csvContent = [
      headers.join(','),
      ...allTransactions.map((t) =>
        [
          t.id,
          t.wholesalerTransactionId,
          `${t.totalAmount.toFixed(2)}`,
          t.userExternalId,
          t.wholesalerUserId,
          t.branchStore,
          stringToLongDateCSVFormatted(t.movementDate),
        ].join(','),
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `liquidaciones-${new Date().toISOString()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      leftIcon={<DownloadIcon />}
      onClick={() => handleDownloadCSV(data)}
      colorScheme="blue"
      variant="outline"
      size="md"
      width={{ base: '100%', md: 'auto' }}
      mt={{ base: 0, md: 8 }}
    >
      Descargar CSV
    </Button>
  );
};

export default DownloadTransfersCSV;
