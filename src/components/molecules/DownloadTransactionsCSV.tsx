import { DownloadIcon } from '@chakra-ui/icons';
import { Button } from '@chakra-ui/react';
import { stringToLongDateCSVFormatted } from '@/utils/date-operations';

const DownloadTransactionsCSV = ({ data }: { data: any }) => {
  const handleDownloadCSV = () => {
    const headers = ['ID de Transacción', 'ID cliente', 'Sucursal', 'Fecha', 'Monto', 'ID Cajero'];

    const csvContent = [
      headers.join(','),
      ...data.map((t: any) =>
        [
          t.wholesalerTransactionId,
          t.externalId,
          t.branchStore,
          stringToLongDateCSVFormatted(t.movementDate),
          t.totalAmount,
          t.wholesalerUserId,
        ].join(','),
      ),
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `transacciones-${new Date().toISOString()}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      leftIcon={<DownloadIcon />}
      onClick={() => handleDownloadCSV()}
      colorScheme="blue"
      variant="outline"
      size="md"
      width={{ base: '100%', md: 'auto' }}
      mt={{ base: 0, md: 8 }}
    >
      Descargar CSV
    </Button>
  );
};

export default DownloadTransactionsCSV;
